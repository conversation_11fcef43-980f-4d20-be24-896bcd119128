import sys

# Add the scripts directory to Python path for importing the patch
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))
import pytorch_load_patch  # noqa: F401  # This automatically applies the torch.load compatibility patch

# Add the scripts directory to Python path for importing the patch
sys.path.insert(0, os.path.join(os.path.dirname(__file__), ".."))
import pytorch_load_patch  # noqa: F401  # This automatically applies the torch.load compatibility patch
