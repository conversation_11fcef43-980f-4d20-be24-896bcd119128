# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""
Script to print all the available environments in Isaac Lab.

The script iterates over all registered environments and stores the details in a table.
It prints the name of the environment, the entry point and the config file.

All the environments are registered in the `lift_zlzk_righthand` extension. They start
with `<PERSON>` in their name.
"""

"""Launch Isaac Sim Simulator first."""

from isaaclab.app import AppLauncher

# launch omniverse app
app_launcher = AppLauncher(headless=True)
simulation_app = app_launcher.app


"""Rest everything follows."""

import gymnasium as gym
from prettytable import PrettyTable

import lift_zlzk_righthand.tasks  # noqa: F401


def main():
    """Print all environments registered in `lift_zlzk_righthand` extension."""
    # print all the available environments
    table = PrettyTable(["S. No.", "Task Name", "Entry Point", "Config"])
    table.title = "Available Environments in Isaac Lab"
    # set alignment of table columns
    table.align["Task Name"] = "l"
    table.align["Entry Point"] = "l"
    table.align["Config"] = "l"

    # count of environments
    index = 0
    # acquire all <PERSON> environments names
    for task_spec in gym.registry.values():
        # Search for environments that start with "Isaac-" (our custom environments)
        # or contain "Template-" (original template environments)
        if task_spec.id.startswith("Isaac-") or "Template-" in task_spec.id:
            # add details to table
            config_entry = task_spec.kwargs.get("env_cfg_entry_point", "N/A")
            table.add_row([index + 1, task_spec.id, task_spec.entry_point, config_entry])
            # increment count
            index += 1

    print(table)
    print(f"\nTotal environments found: {index}")


if __name__ == "__main__":
    try:
        # run the main function
        main()
    except Exception as e:
        raise e
    finally:
        # close the app
        simulation_app.close()
