seed: 42


# Models are instantiated using skrl's model instantiator utility
# https://skrl.readthedocs.io/en/latest/api/utils/model_instantiators.html
models:
  separate: True  # False -> True (matching rl_games network separate)
  policy:  # see gaussian_model parameters
    class: GaussianMixin
    clip_actions: True  # False
    clip_log_std: True
    min_log_std: -20.0
    max_log_std: 2.0
    initial_log_std: 0.0 # 0.0 (matching rl_games fixed_sigma)
    network:
      - name: net
        input: STATES
        layers: [512, 256, 128]  # [384, 192, 96] -> [512, 256, 128] (matching rl_games mlp units)
        activations: elu
    output: ACTIONS
  value:  # see deterministic_model parameters
    class: DeterministicMixin
    clip_actions: False
    network:
      - name: net
        input: STATES
        layers: [512, 256, 128]  # [384, 192, 96] -> [512, 256, 128] (matching rl_games mlp units)
        activations: elu
    output: ONE


# Rollout memory
# https://skrl.readthedocs.io/en/latest/api/memories/random.html
memory:
  class: RandomMemory
  memory_size: -1  # automatically determined (same as agent:rollouts)


# PPO agent configuration (field names are from PPO_DEFAULT_CONFIG)
# https://skrl.readthedocs.io/en/latest/api/agents/ppo.html
agent:
  class: PPO
  rollouts: 36  # 24 -> 36 (matching rl_games horizon_length)
  learning_epochs: 5  # 8 -> 5 (matching rl_games mini_epochs)
  mini_batches: 4  # 4
  discount_factor: 0.99
  lambda: 0.95
  learning_rate: 1.0e-03  # 1.0e-04 -> 1.0e-03 (matching rl_games learning_rate)
  
  learning_rate_scheduler: KLAdaptiveLR
  learning_rate_scheduler_kwargs:
    kl_threshold: 0.01  # 0.01
  state_preprocessor: RunningStandardScaler
  state_preprocessor_kwargs: null
  value_preprocessor: RunningStandardScaler
  value_preprocessor_kwargs: null
  random_timesteps: 0
  learning_starts: 0
  grad_norm_clip: 1.0
  ratio_clip: 0.2  # 0.2 (matching rl_games e_clip)
  value_clip: 0.2
  clip_predicted_values: True  # matching rl_games clip_value
  entropy_loss_scale: 0.001  # 0.001 (matching rl_games entropy_coef)
  value_loss_scale: 4.0  # 2.0 -> 4.0 (matching rl_games critic_coef)
  kl_threshold: 0.0
  rewards_shaper_scale: 0.01
  time_limit_bootstrap: False
  # logging and checkpoint
  experiment:
    directory: "lift_zlzk_righthand"
    experiment_name: ""
    write_interval: 100      # 每100步写入一次日志
    checkpoint_interval: 1000  # 每1000步保存一次检查点


# Sequential trainer
# https://skrl.readthedocs.io/en/latest/api/trainers/sequential.html
trainer:
  class: SequentialTrainer
  timesteps: 750000  # 100000 -> 750000 (matching rl_games max_epochs * horizon_length)
  environment_info: log
