# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from lift_zlzk_righthand.assets.rohand_realman_right import ROHAND_REALMAN_RIGHT_CFG  # isort: skip

from isaaclab.managers import ObservationTermCfg as ObsTerm
from isaaclab.managers import RewardTermCfg as RewTerm
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors import ContactSensorCfg
from isaaclab.utils import configclass

from ... import dexsuite_env_cfg as dexsuite
from ... import mdp


@configclass
class RealmanRohandRelJointPosActionCfg:
    action = mdp.RelativeJointPositionActionCfg(asset_name="robot", joint_names=[".*"], scale=0.1)


@configclass
class RealmanRohandDrvRelJointPosActionCfg:
    action = mdp.DrivenRelativeJointActionCfg(
        asset_name="robot",
        joint_names=[
            "joint[1-7]_right",
            "righthand_th_root_link",
            "righthand_th_proximal_link",
            "righthand_if_proximal_link",
            "righthand_mf_proximal_link",
            "righthand_rf_proximal_link",
            "righthand_lf_proximal_link",
        ],
        primary_joint_patterns=[
            "righthand_th_proximal_link",
            "righthand_if_proximal_link",
            "righthand_mf_proximal_link",
            "righthand_rf_proximal_link",
            "righthand_lf_proximal_link",
        ],
        driven_joint_patterns={
            "righthand_th_proximal_link": "righthand_th_distal_link",
            "righthand_if_proximal_link": "righthand_if_distal_link",
            "righthand_mf_proximal_link": "righthand_mf_distal_link",
            "righthand_rf_proximal_link": "righthand_rf_distal_link",
            "righthand_lf_proximal_link": "righthand_lf_distal_link",
        },
        default_ratio=1.0,
        scale=0.1,
        ratio_overrides={
            "righthand_th_proximal_link": 0.6,
            "righthand_if_proximal_link": 1.05,
            "righthand_mf_proximal_link": 1.00,
            "righthand_rf_proximal_link": 1.00,
            "righthand_lf_proximal_link": 0.95,
        },
        extend_joint_names=False,  # 不自动扩展从动关节到action中
    )


@configclass
class RealmanRohandReorientRewardCfg(dexsuite.RewardsCfg):

    # bool awarding term if 2 finger tips are in contact with object, one of the contacting fingers has to be thumb.
    good_finger_contact = RewTerm(
        func=mdp.contacts,
        weight=0.5,
        params={"threshold": 1.0},
    )


@configclass
class RealmanRohandMixinCfg:
    rewards: RealmanRohandReorientRewardCfg = RealmanRohandReorientRewardCfg()
    actions: RealmanRohandDrvRelJointPosActionCfg = RealmanRohandDrvRelJointPosActionCfg()
    # actions: RealmanRohandDrvRelJointPosActionCfg = RealmanRohandRelJointPosActionCfg()

    def __post_init__(self: dexsuite.DexsuiteReorientEnvCfg):
        super().__post_init__()

        # Set ZLZK as robot
        self.scene.robot = ROHAND_REALMAN_RIGHT_CFG.replace(prim_path="{ENV_REGEX_NS}/Robot")

        # Set the body name for the end effector
        self.commands.object_pose.body_name = "ee_right"

        finger_tip_body_list = ["righthand_if_distal_link",
                                "righthand_mf_distal_link",
                                "righthand_rf_distal_link",
                                "righthand_lf_distal_link",
                                "righthand_th_distal_link"]
        for link_name in finger_tip_body_list:
            setattr(
                self.scene,
                f"{link_name}_object_s",
                ContactSensorCfg(
                    prim_path="{ENV_REGEX_NS}/Robot/" + link_name,
                    filter_prim_paths_expr=["{ENV_REGEX_NS}/Object"],
                ),
            )
        self.observations.policy.contact = ObsTerm(
            func=mdp.fingers_contact_force_b,
            params={"contact_sensor_names": [f"{link}_object_s" for link in finger_tip_body_list], "flatten": True},
            clip=(-20.0, 20.0),  # contact force in finger tips is under 20N normally
        )
        # Keep concatenation enabled but flatten contact forces to 1D
        self.observations.policy.concatenate_terms = True
        self.observations.policy.hand_tips_state_b.params["body_asset_cfg"].body_names = ["ee_right", ".*_tip"]
        self.rewards.fingers_to_object.params["asset_cfg"] = SceneEntityCfg("robot", body_names=["ee_right", ".*_tip"])


@configclass
class DexsuiteRealmanRohandReorientEnvCfg(RealmanRohandMixinCfg, dexsuite.DexsuiteReorientEnvCfg):
    pass


@configclass
class DexsuiteRealmanRohandReorientEnvCfg_PLAY(RealmanRohandMixinCfg, dexsuite.DexsuiteReorientEnvCfg_PLAY):
    pass


@configclass
class DexsuiteRealmanRohandLiftEnvCfg(RealmanRohandMixinCfg, dexsuite.DexsuiteLiftEnvCfg):
    pass


@configclass
class DexsuiteRealmanRohandLiftEnvCfg_PLAY(RealmanRohandMixinCfg, dexsuite.DexsuiteLiftEnvCfg_PLAY):
    pass
