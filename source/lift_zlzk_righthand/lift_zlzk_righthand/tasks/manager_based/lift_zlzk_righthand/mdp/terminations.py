# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Common functions that can be used to activate certain terminations for the lift task.

The functions can be passed to the :class:`isaaclab.managers.TerminationTermCfg` object to enable
the termination introduced by the function.
"""

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import RigidObject, Articulation
from isaaclab.managers import SceneEntityCfg
from isaaclab.utils.math import combine_frame_transforms

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv


def object_reached_goal(
    env: ManagerBasedRLEnv,
    command_name: str = "object_pose",
    threshold: float = 0.02,
    robot_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
) -> torch.Tensor:
    """Termination condition for the object reaching the goal position.

    Args:
        env: The environment.
        command_name: The name of the command that is used to control the object.
        threshold: The threshold for the object to reach the goal position. Defaults to 0.02.
        robot_cfg: The robot configuration. Defaults to SceneEntityCfg("robot").
        object_cfg: The object configuration. Defaults to SceneEntityCfg("object").

    """
    # extract the used quantities (to enable type-hinting)
    robot: RigidObject = env.scene[robot_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    command = env.command_manager.get_command(command_name)
    # compute the desired position in the world frame
    des_pos_b = command[:, :3]
    des_pos_w, _ = combine_frame_transforms(robot.data.root_pos_w, robot.data.root_quat_w, des_pos_b)
    # distance of the end-effector to the object: (num_envs,)
    distance = torch.norm(des_pos_w - object.data.root_pos_w[:, :3], dim=1)

    # rewarded if the object is lifted above the threshold
    return distance < threshold


def out_of_bound(
    env: ManagerBasedRLEnv,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    in_bound_range: dict[str, tuple[float, float]] = {},
    debug: bool = False,
) -> torch.Tensor:
    """Termination condition for the object falls out of bound.

    Args:
        env: The environment.
        asset_cfg: The object configuration. Defaults to SceneEntityCfg("object").
        in_bound_range: The range in x, y, z such that the object is considered in range
    """
    object: RigidObject = env.scene[asset_cfg.name]
    range_list = [in_bound_range.get(key, (0.0, 0.0)) for key in ["x", "y", "z"]]
    ranges = torch.tensor(range_list, device=env.device)

    object_pos_local = object.data.root_pos_w - env.scene.env_origins
    outside_bounds = ((object_pos_local < ranges[:, 0]) | (object_pos_local > ranges[:, 1])).any(dim=1)

    # Debug information
    if debug and torch.any(outside_bounds):
        print("[DEBUG] Out of bounds detected!")
        print(f"[DEBUG] Object positions (local): {object_pos_local}")
        print(f"[DEBUG] Bounds: x{ranges[0]}, y{ranges[1]}, z{ranges[2]}")
        print(f"[DEBUG] Outside bounds mask: {outside_bounds}")
        print(f"[DEBUG] Number of out-of-bounds envs: {outside_bounds.sum().item()}")

        # Show which axis is out of bounds for each environment
        x_out = (object_pos_local[:, 0] < ranges[0, 0]) | (object_pos_local[:, 0] > ranges[0, 1])
        y_out = (object_pos_local[:, 1] < ranges[1, 0]) | (object_pos_local[:, 1] > ranges[1, 1])
        z_out = (object_pos_local[:, 2] < ranges[2, 0]) | (object_pos_local[:, 2] > ranges[2, 1])

        if torch.any(x_out):
            print(f"[DEBUG] X-axis violations: {x_out.nonzero().flatten()}")
        if torch.any(y_out):
            print(f"[DEBUG] Y-axis violations: {y_out.nonzero().flatten()}")
        if torch.any(z_out):
            print(f"[DEBUG] Z-axis violations: {z_out.nonzero().flatten()}")

    return outside_bounds


def abnormal_robot_state(
    env: ManagerBasedRLEnv,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    joint_names_expr: list[str] | None = None,
) -> torch.Tensor:
    """Terminating environment when violation of velocity limits detects, this usually indicates unstable physics caused
    by very bad, or aggressive action

    Args:
        env: The environment instance
        asset_cfg: The robot asset configuration
        joint_names_expr: List of joint name expressions to check. If None, checks all joints.
                         Example: ["joint[1-7]_right"] to check only arm joints
    """
    robot: Articulation = env.scene[asset_cfg.name]

    # print("[DEBUG] Checking abnormal robot state...")

    # Get joint velocities and limits
    joint_vel = robot.data.joint_vel.abs()  # (num_envs, num_joints)
    joint_vel_limits = robot.data.joint_vel_limits  # (num_envs, num_joints)

    # If specific joints are specified, filter to only those joints
    if joint_names_expr is not None:
        import isaaclab.utils.string as string_utils

        # Find matching joint indices
        joint_indices = []
        for expr in joint_names_expr:
            # resolve_matching_names returns (indices, names) tuple
            matching_indices, _ = string_utils.resolve_matching_names(expr, robot.joint_names)
            joint_indices.extend(matching_indices)

        # Remove duplicates and sort
        joint_indices = sorted(list(set(joint_indices)))

        # Debug: print matched joints (uncomment for debugging)
        # if len(joint_indices) > 0:
        #     matched_joint_names = [robot.joint_names[i] for i in joint_indices]
        #     print(f"[DEBUG] Checking abnormal state for {len(joint_indices)} joints: {matched_joint_names}")

        if len(joint_indices) > 0:
            # Only check specified joints
            joint_vel = joint_vel[:, joint_indices]
            joint_vel_limits = joint_vel_limits[:, joint_indices]

    # Check if any joint exceeds 2x its velocity limit
    return (joint_vel > (joint_vel_limits * 2)).any(dim=1)
