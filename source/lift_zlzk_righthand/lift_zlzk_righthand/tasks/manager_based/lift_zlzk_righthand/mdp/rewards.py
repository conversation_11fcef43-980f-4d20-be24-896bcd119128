# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

import torch
from typing import TYPE_CHECKING

from isaaclab.assets import RigidObject, Articulation
from isaaclab.managers import SceneEntityCfg
from isaaclab.sensors import ContactSensor
from isaaclab.utils import math as math_utils
from isaaclab.utils.math import combine_frame_transforms, compute_pose_error, quat_apply

if TYPE_CHECKING:
    from isaaclab.envs import ManagerBasedRLEnv


def action_rate_l2_clamped(env: ManagerBasedRLEnv) -> torch.Tensor:
    """Penalize the rate of change of the actions using L2 squared kernel."""
    return torch.sum(torch.square(env.action_manager.action - env.action_manager.prev_action), dim=1).clamp(-1000, 1000)


def action_l2_clamped(env: ManagerBasedRLEnv) -> torch.Tensor:
    """Penalize the actions using L2 squared kernel."""
    return torch.sum(torch.square(env.action_manager.action), dim=1).clamp(-1000, 1000)


def object_ee_distance(
    env: ManagerBasedRLEnv,
    std: float,
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
) -> torch.Tensor:
    """Reward reaching the object using a tanh-kernel on end-effector distance.

    The reward is close to 1 when the maximum distance between the object and any end-effector body is small.
    """
    asset: RigidObject = env.scene[asset_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]
    asset_pos = asset.data.body_pos_w[:, asset_cfg.body_ids]
    object_pos = object.data.root_pos_w
    object_ee_distance = torch.norm(asset_pos - object_pos[:, None, :], dim=-1).max(dim=-1).values
    return 1 - torch.tanh(object_ee_distance / std)


def contacts(env: ManagerBasedRLEnv, threshold: float) -> torch.Tensor:
    """Penalize undesired contacts as the number of violations that are above a threshold."""

    thumb_contact_sensor: ContactSensor = env.scene.sensors["righthand_th_distal_link_object_s"]
    index_contact_sensor: ContactSensor = env.scene.sensors["righthand_if_distal_link_object_s"]
    middle_contact_sensor: ContactSensor = env.scene.sensors["righthand_mf_distal_link_object_s"]
    ring_contact_sensor: ContactSensor = env.scene.sensors["righthand_rf_distal_link_object_s"]
    little_contact_sensor: ContactSensor = env.scene.sensors["righthand_lf_distal_link_object_s"]
    # check if contact force is above threshold
    thumb_contact = thumb_contact_sensor.data.force_matrix_w.view(env.num_envs, 3)
    index_contact = index_contact_sensor.data.force_matrix_w.view(env.num_envs, 3)
    middle_contact = middle_contact_sensor.data.force_matrix_w.view(env.num_envs, 3)
    ring_contact = ring_contact_sensor.data.force_matrix_w.view(env.num_envs, 3)
    little_contact = little_contact_sensor.data.force_matrix_w.view(env.num_envs, 3)

    thumb_contact_mag = torch.norm(thumb_contact, dim=-1)
    index_contact_mag = torch.norm(index_contact, dim=-1)
    middle_contact_mag = torch.norm(middle_contact, dim=-1)
    ring_contact_mag = torch.norm(ring_contact, dim=-1)
    little_contact_mag = torch.norm(little_contact, dim=-1)

    # 打印每个传感器的mag值
    # 只在有接触力时才打印（避免全零输出）- 注意mag是张量，取第一个值判断
    # if (thumb_contact_mag[0] > 0 or index_contact_mag[0] > 0 or middle_contact_mag[0] > 0 or
    #         ring_contact_mag[0] > 0 or little_contact_mag[0] > 0):
    #     print(f"Contact forces - Thumb: {thumb_contact_mag[0]:.3f}, Index: {index_contact_mag[0]:.3f}, "
    #           f"Middle: {middle_contact_mag[0]:.3f}, Ring: {ring_contact_mag[0]:.3f}, Little: {little_contact_mag[0]:.3f}")

    good_contact_cond1 = (thumb_contact_mag > threshold) & (
        (index_contact_mag > threshold) | (middle_contact_mag > threshold) | (ring_contact_mag > threshold) | (little_contact_mag > threshold)
    )

    return good_contact_cond1


def success_reward(
    env: ManagerBasedRLEnv,
    command_name: str,
    asset_cfg: SceneEntityCfg,
    align_asset_cfg: SceneEntityCfg,
    pos_std: float,
    rot_std: float | None = None,
) -> torch.Tensor:
    """Reward success by comparing commanded pose to the object pose using tanh kernels on error."""

    asset: RigidObject = env.scene[asset_cfg.name]
    object: RigidObject = env.scene[align_asset_cfg.name]
    command = env.command_manager.get_command(command_name)
    des_pos_w, des_quat_w = combine_frame_transforms(
        asset.data.root_pos_w, asset.data.root_quat_w, command[:, :3], command[:, 3:7]
    )
    pos_err, rot_err = compute_pose_error(des_pos_w, des_quat_w, object.data.root_pos_w, object.data.root_quat_w)
    pos_dist = torch.norm(pos_err, dim=1)
    if not rot_std:
        # square is not necessary but this help to keep the final value between having rot_std or not roughly the same
        return (1 - torch.tanh(pos_dist / pos_std)) ** 2
    rot_dist = torch.norm(rot_err, dim=1)
    return (1 - torch.tanh(pos_dist / pos_std)) * (1 - torch.tanh(rot_dist / rot_std))


def position_command_error_tanh(
    env: ManagerBasedRLEnv, std: float, command_name: str, asset_cfg: SceneEntityCfg, align_asset_cfg: SceneEntityCfg
) -> torch.Tensor:
    """Reward tracking of commanded position using tanh kernel, gated by contact presence."""

    asset: RigidObject = env.scene[asset_cfg.name]
    object: RigidObject = env.scene[align_asset_cfg.name]
    command = env.command_manager.get_command(command_name)
    # obtain the desired and current positions
    des_pos_b = command[:, :3]
    des_pos_w, _ = combine_frame_transforms(asset.data.root_pos_w, asset.data.root_quat_w, des_pos_b)
    distance = torch.norm(object.data.root_pos_w - des_pos_w, dim=1)
    return (1 - torch.tanh(distance / std)) * contacts(env, 1.0).float()


def orientation_command_error_tanh(
    env: ManagerBasedRLEnv, std: float, command_name: str, asset_cfg: SceneEntityCfg, align_asset_cfg: SceneEntityCfg
) -> torch.Tensor:
    """Reward tracking of commanded orientation using tanh kernel, gated by contact presence."""

    asset: RigidObject = env.scene[asset_cfg.name]
    object: RigidObject = env.scene[align_asset_cfg.name]
    command = env.command_manager.get_command(command_name)
    # obtain the desired and current orientations
    des_quat_b = command[:, 3:7]
    des_quat_w = math_utils.quat_mul(asset.data.root_state_w[:, 3:7], des_quat_b)
    quat_distance = math_utils.quat_error_magnitude(object.data.root_quat_w, des_quat_w)

    return (1 - torch.tanh(quat_distance / std)) * contacts(env, 1.0).float()


def ee_object_axis_alignment(
    env: ManagerBasedRLEnv,
    std: float = 1.0,
    ee_frame_cfg: SceneEntityCfg = SceneEntityCfg("robot", body_names=["ee_right"]),
    object_cfg: SceneEntityCfg = SceneEntityCfg("object"),
) -> torch.Tensor:
    """奖励末端执行器X轴与目标物体Z轴方向对齐。

    Args:
        env: 环境实例
        std: 用于tanh核函数的标准差，控制奖励的平滑程度
        ee_frame_cfg: 末端执行器配置，使用机器人的ee_right body
        object_cfg: 目标物体配置

    Returns:
        奖励值张量，范围约为[0, 1]
    """
    # 获取机器人和目标物体
    robot: Articulation = env.scene[ee_frame_cfg.name]
    object: RigidObject = env.scene[object_cfg.name]

    # 获取末端执行器的body ID
    ee_body_id = robot.find_bodies("ee_right")[0]

    # 获取末端执行器的四元数
    ee_quat_w = robot.data.body_quat_w[:, ee_body_id]  # (num_envs, 4)

    # 获取目标物体的四元数
    object_quat = object.data.root_quat_w  # (num_envs, 4)

    # 使用quat_apply函数获取轴向量，避免维度问题
    # 定义标准轴向量
    x_axis = torch.tensor([1.0, 0.0, 0.0], device=ee_quat_w.device, dtype=ee_quat_w.dtype)  # X轴
    z_axis = torch.tensor([0.0, 0.0, 1.0], device=object_quat.device, dtype=object_quat.dtype)  # Z轴

    # 将标准轴向量扩展到批次维度
    x_axis_batch = x_axis.unsqueeze(0).expand(ee_quat_w.shape[0], -1)  # (num_envs, 3)
    z_axis_batch = z_axis.unsqueeze(0).expand(object_quat.shape[0], -1)  # (num_envs, 3)

    # 使用四元数旋转标准轴向量得到实际轴向量
    ee_x_axis = quat_apply(ee_quat_w, x_axis_batch)  # 末端执行器X轴方向 (num_envs, 3)
    object_z_axis = quat_apply(object_quat, z_axis_batch)  # 目标物体Z轴方向 (num_envs, 3)

    # 计算两个轴的点积（余弦相似度）
    dot_product = torch.sum(ee_x_axis * object_z_axis, dim=1)  # (num_envs,)

    # 将点积从[-1, 1]映射到[0, 1]，然后使用std参数控制敏感性
    alignment_score = (dot_product + 1.0) / 2.0  # 映射到[0, 1]

    # 使用末端敏感的奖励函数，在接近完全对齐时更敏感
    reward = 1.0 - torch.tanh((1.0 - alignment_score) / std)

    return reward


def print_joint_torques(
    env: ManagerBasedRLEnv,
    asset_cfg: SceneEntityCfg = SceneEntityCfg("robot"),
    print_frequency: int = 100,  # 每100步打印一次
) -> torch.Tensor:
    """打印机械臂每个关节的实时扭矩信息

    Args:
        env: 环境实例
        asset_cfg: 机器人配置
        print_frequency: 打印频率，每N步打印一次

    Returns:
        torch.Tensor: 返回0，不影响奖励计算
    """
    robot: Articulation = env.scene[asset_cfg.name]

    # 控制打印频率
    if hasattr(env, '_torque_print_counter'):
        env._torque_print_counter += 1
    else:
        env._torque_print_counter = 0

    if env._torque_print_counter % print_frequency == 0:
        # 获取关节扭矩数据
        joint_torques = robot.data.applied_torque  # shape: (num_envs, num_joints)
        joint_names = robot.joint_names

        print(f"\n[DEBUG] Joint Torques at step {env._torque_print_counter}")
        print("=" * 80)

        # 只打印第一个环境的数据（避免输出过多）
        env_idx = 0
        if env.num_envs > env_idx:
            torques = joint_torques[env_idx]  # shape: (num_joints,)

            print(f"Environment {env_idx}:")
            for joint_name, torque in zip(joint_names, torques):
                print(f"  {joint_name:25s}: {torque.item():8.4f} Nm")

            # 统计信息
            max_torque = torch.max(torch.abs(torques))
            avg_torque = torch.mean(torch.abs(torques))
            print("\n  Statistics:")
            print(f"    Max absolute torque: {max_torque.item():8.4f} Nm")
            print(f"    Avg absolute torque: {avg_torque.item():8.4f} Nm")

            # 检查是否有异常大的扭矩
            torque_limit_check = torch.abs(torques) > 100.0  # 假设100Nm为警告阈值
            if torch.any(torque_limit_check):
                warning_joints = [joint_names[idx] for idx in range(len(joint_names)) if torque_limit_check[idx]]
                print(f"    ⚠️  High torque warning: {warning_joints}")

        print("=" * 80)

    # 返回0，不影响奖励计算
    return torch.zeros(env.num_envs, device=env.device)
