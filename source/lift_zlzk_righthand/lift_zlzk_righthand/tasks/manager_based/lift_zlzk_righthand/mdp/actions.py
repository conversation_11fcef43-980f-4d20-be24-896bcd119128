# Copyright (c) 2022-2025, The Isaac Lab Project Developers.
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

from __future__ import annotations

from dataclasses import field
from typing import Dict, List

from isaaclab.utils import configclass

# Base action config (relative joint position style)
from isaaclab.envs.mdp.actions.actions_cfg import RelativeJointPositionActionCfg
from isaaclab.envs.mdp.actions import RelativeJointPositionAction


@configclass
class DrivenRelativeJointActionCfg(RelativeJointPositionActionCfg):
    """Configuration for driven relative joint action using relative joint position control.

    This action extends RelativeJointPositionActionCfg to support primary-driven joint coupling,
    where primary joints control both themselves and their associated driven joints through
    a mapping relationship. The action uses relative position control for smooth and safe
    joint movements.

    Key features:
    - Primary-driven joint coupling with configurable ratios
    - Map-based driven joint patterns for flexible joint relationships
    - Support for independent primary joints (without driven counterparts)
    - Relative position control for incremental joint movements
    - Automatic validation of joint pattern consistency

    Example usage:
         ```python
         action_cfg = DrivenBinaryJointActionCfg(
             asset_name="robot",
             joint_names=["finger_.*"],
             primary_joint_patterns=["finger_1", "finger_2", "finger_3"],
             driven_joint_patterns={"finger_1": "finger_1_tip", "finger_2": "finger_2_tip"},
             ratio_overrides={"finger_1": 0.8, "finger_2": 0.6},
             # finger_3 remains independent (no driven joint)
             default_ratio=1.0,
             scale=0.1,
         )
         ```
    """

    class_type: type = RelativeJointPositionAction

    # Primary joint patterns that can act independently or drive other joints
    primary_joint_patterns: List[str] = field(
        default_factory=lambda: [
            "righthand_th_proximal_link",
            "righthand_(if|mf|rf|lf)_proximal_link",
        ]
    )
    # Driven joint patterns controlled by primary joints
    # Map from primary joint pattern to driven joint pattern
    # Keys must exist in primary_joint_patterns for validation
    # Primary joints without entries here act independently
    driven_joint_patterns: Dict[str, str] = field(
        default_factory=lambda: {
            "righthand_th_proximal_link": "righthand_th_distal_link",
            "righthand_(if|mf|rf|lf)_proximal_link": "righthand_(if|mf|rf|lf)_distal_link",
        }
    )

    # Global default coupling ratio (driven = ratio * primary)
    default_ratio: float = 1.0
    # Optional per-primary-pattern ratio overrides for fine-tuned control
    # Map from primary joint pattern to coupling ratio (driven = ratio * primary)
    # Keys must exist in primary_joint_patterns for validation
    # Primary joints without entries here use the default_ratio
    ratio_overrides: Dict[str, float] = field(default_factory=dict)

    # Retain ability to configure primary joints' open/close expressions
    # If the base class already defines these, these fields will simply override/extend them.
    open_command_expr: Dict[str, float] = field(default_factory=dict)
    close_command_expr: Dict[str, float] = field(default_factory=dict)

    # Whether to automatically extend joint_names to include driven_joint_patterns
    extend_joint_names: bool = True

    def __post_init__(self) -> None:
        # Call parent post-init if present
        try:
            super().__post_init__()  # type: ignore[misc]
        except Exception:
            pass

        # Validate that all keys in driven_joint_patterns exist in primary_joint_patterns
        if self.driven_joint_patterns:
            primary_set = set(self.primary_joint_patterns)
            for primary_pattern in self.driven_joint_patterns.keys():
                if primary_pattern not in primary_set:
                    raise ValueError(
                        f"Primary pattern '{primary_pattern}' in driven_joint_patterns "
                        f"must exist in primary_joint_patterns: {self.primary_joint_patterns}"
                    )

        # Validate that all keys in ratio_overrides exist in primary_joint_patterns
        if self.ratio_overrides:
            primary_set = set(self.primary_joint_patterns)
            for primary_pattern in self.ratio_overrides.keys():
                if primary_pattern not in primary_set:
                    raise ValueError(
                        f"Primary pattern '{primary_pattern}' in ratio_overrides "
                        f"must exist in primary_joint_patterns: {self.primary_joint_patterns}"
                    )

        # Prepare driven open/close expressions derived from primary expressions using ratios
        derived_open_expr: Dict[str, float] = {}
        derived_close_expr: Dict[str, float] = {}

        # Helper: fetch ratio for a given primary pattern
        def ratio_for(primary_pattern: str) -> float:
            return self.ratio_overrides.get(primary_pattern, self.default_ratio)

        # Build derived expressions from primary -> driven pattern mapping
        for p_pattern, d_pattern in self.driven_joint_patterns.items():
            r = ratio_for(p_pattern)
            # Only add derived entries if the driven pattern is not already explicitly specified
            if d_pattern not in self.open_command_expr and p_pattern in self.open_command_expr:
                derived_open_expr[d_pattern] = self.open_command_expr[p_pattern] * r
            if d_pattern not in self.close_command_expr and p_pattern in self.close_command_expr:
                derived_close_expr[d_pattern] = self.close_command_expr[p_pattern] * r

        # Merge derived expressions with user-provided ones (don't overwrite explicit user configs)
        for k, v in derived_open_expr.items():
            if k not in self.open_command_expr:
                self.open_command_expr[k] = v
        for k, v in derived_close_expr.items():
            if k not in self.close_command_expr:
                self.close_command_expr[k] = v

        # Optionally ensure driven joints are part of the action's joint set
        if self.extend_joint_names:
            try:
                # joint_names can be names or regex patterns — we just ensure the driven patterns are included
                existing = set(self.joint_names) if getattr(self, "joint_names", None) else set()
                for d in self.driven_joint_patterns.values():
                    if d not in existing:
                        # Append to existing joint names/patterns list
                        if getattr(self, "joint_names", None) is None:
                            self.joint_names = []  # type: ignore[attr-defined]
                        self.joint_names.append(d)  # type: ignore[attr-defined]
                        existing.add(d)
            except Exception:
                # If joint_names is not available in the base cfg, silently ignore
                pass
