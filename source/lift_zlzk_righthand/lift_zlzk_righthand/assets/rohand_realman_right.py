# Copyright (c) 2022-2025, The Isaac Lab Project Developers (https://github.com/isaac-sim/IsaacLab/blob/main/CONTRIBUTORS.md).
# All rights reserved.
#
# SPDX-License-Identifier: BSD-3-Clause

"""Configuration for the Wheelbot arm (right) with ROHand attached (local USD).

This configuration spawns the articulated robot from the local USD file:
D:\\zoomlion\\IsaacLab\\usd_files\\wheelbot-righthand\\wheelbot-rightarm-rohand.usda

Active DOFs on the dexterous hand: total 6 — thumb has two DOFs (root, proximal);
index/middle/ring/little each has one DOF (proximal). Other joints are passive (not actuated).
"""

from __future__ import annotations

import isaaclab.sim as sim_utils
from isaaclab.actuators import ImplicitActuatorCfg
from isaaclab.assets.articulation import ArticulationCfg

##
# Configuration
##

ROHAND_REALMAN_RIGHT_CFG = ArticulationCfg(
    prim_path="{ENV_REGEX_NS}/Robot",
    spawn=sim_utils.UsdFileCfg(
        # usd_path=r"D:\zoomlion\lift_zlzk_righthand\usd_files\wheelbot-rightarm-rohand.usda",
        usd_path="/home/<USER>/lift_zlzk_righthand/usd_files/wheelbot-rightarm-rohand.usda",
        activate_contact_sensors=True,  # Enable contact sensors for ContactSensor functionality
        rigid_props=sim_utils.RigidBodyPropertiesCfg(
            disable_gravity=False,
            max_depenetration_velocity=10.0,
            enable_gyroscopic_forces=True,
        ),
        articulation_props=sim_utils.ArticulationRootPropertiesCfg(
            enabled_self_collisions=False,
            solver_position_iteration_count=4,
            solver_velocity_iteration_count=0,
            sleep_threshold=0.005,
            stabilization_threshold=0.001,
        ),
        copy_from_source=False,
    ),
    init_state=ArticulationCfg.InitialStateCfg(
        pos=(0.0, 0.0, 0.5),
        joint_pos={
            # 7-DoF arm (right)
            "joint1_right": 0.7,
            "joint2_right": 1.0283,
            "joint3_right": -2.5897,
            "joint4_right": -0.7754,
            "joint5_right": -0.7140,
            "joint6_right": 1.5,
            # "joint7_right": 3.2385,
            "joint7_right": 2.4000,
            # "joint7_right": 1.7385,
            # ROHand (right) active DOFs: thumb root + proximal, and proximal for IF/MF/RF/LF
            "righthand_th_root_link": 1.5708,
            "righthand_th_proximal_link": 0.5,
            "righthand_if_proximal_link": 0.087,
            "righthand_mf_proximal_link": 0.087,
            "righthand_rf_proximal_link": 0.087,
            "righthand_lf_proximal_link": 0.087,
            "righthand_th_distal_link": 0.0,
            "righthand_if_distal_link": 0.0,
            "righthand_mf_distal_link": 0.0,
            "righthand_rf_distal_link": 0.0,
            "righthand_lf_distal_link": 0.0,
        },
    ),
    actuators={
        # Arm joints (revolute)
        "arm": ImplicitActuatorCfg(
            joint_names_expr=["joint[1-7]_right"],
            # 推荐使用分关节的扭矩限制，更符合实际机器人规格
            effort_limit_sim={
                "joint1_right": 180.0,   # 基座关节，承载大负载
                "joint2_right": 180.0,   # 肩部关节，承载大负载
                "joint3_right": 90.0,    # 肘部关节，中等负载
                "joint4_right": 90.0,    # 前臂关节，中等负载
                "joint5_right": 30.0,    # 手腕关节，小负载
                "joint6_right": 30.0,    # 手腕关节，小负载
                "joint7_right": 30.0,    # 末端关节，小负载
            },
            # effort_limit_sim=300.0,  # 统一限制（不推荐，过于粗糙）
            # 合理的机械臂关节速度限制 (rad/s) - 使用velocity_limit_sim
            velocity_limit_sim={
                "joint1_right": 3.14,    # 180 deg/s - 基座旋转
                "joint2_right": 2.62,    # 150 deg/s - 肩部俯仰
                "joint3_right": 3.49,    # 200 deg/s - 肘部旋转
                "joint4_right": 3.49,    # 200 deg/s - 前臂俯仰
                "joint5_right": 4.19,    # 240 deg/s - 手腕旋转
                "joint6_right": 4.19,    # 240 deg/s - 手腕俯仰
                "joint7_right": 5.24,    # 300 deg/s - 手腕末端旋转
            },
            stiffness=100.0,
            damping=20.0,
            friction=1.0,
        ),
        # ROHand revolute joints: only 6 active DOFs (thumb: root+proximal; IF/MF/RF/LF: proximal)
        "hand_revolute": ImplicitActuatorCfg(
            joint_names_expr=[
                "righthand_th_root_link",
                "righthand_th_proximal_link",
                "righthand_if_proximal_link",
                "righthand_mf_proximal_link",
                "righthand_rf_proximal_link",
                "righthand_lf_proximal_link",
            ],
            effort_limit_sim=20.0,
            # 合理的手指关节速度限制 (rad/s) - 使用velocity_limit_sim
            velocity_limit_sim=2.09,  # 120 deg/s - 手指主动关节
            stiffness=5.0,
            damping=0.1,
            friction=0.01,
        ),
        "hand_passive_damper": ImplicitActuatorCfg(
            joint_names_expr=[
                "righthand_(if|mf|rf|lf|th)_distal_link",
            ],
            effort_limit_sim=1000.0,
            # 合理的被动关节速度限制 (rad/s) - 使用velocity_limit_sim
            velocity_limit_sim=1.57,  # 90 deg/s - 被动关节，较低速度
            stiffness=100.0,
            damping=2.0,
            friction=0.01,
        ),  # 被动关节，此处模拟物理上的四连杆结构，由于需要保持四连杆的约束关系，应该给予高刚度低阻尼以限制其自由运动。
    },
    soft_joint_pos_limit_factor=1.0,
)
"""Configuration for the Wheelbot right arm with ROHand (from local USD)."""
