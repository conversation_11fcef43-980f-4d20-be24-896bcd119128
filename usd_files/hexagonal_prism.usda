#usda 1.0
(
    defaultPrim = "HexagonalPrism"
    metersPerUnit = 1
    upAxis = "Z"
)

def Xform "HexagonalPrism" (
    kind = "component"
    prepend apiSchemas = ["PhysicsRigidBodyAPI", "PhysicsMassAPI"]
)
{
    # Physics properties
    bool physics:rigidBodyEnabled = true
    bool physics:kinematicEnabled = false
    float physics:mass = 0.05
    
    def Mesh "geometry" (
        prepend apiSchemas = ["PhysicsCollisionAPI", "PhysicsMeshCollisionAPI"]
    )
    {
        # Collision properties
        bool physics:collisionEnabled = true
        uniform token physics:approximation = "convexHull"
        
        # Mesh geometry
        int[] faceVertexCounts = [6, 6, 4, 4, 4, 4, 4, 4]
        int[] faceVertexIndices = [
            # Top face (hexagon)
            0, 1, 2, 3, 4, 5,
            # Bottom face (hexagon)
            6, 11, 10, 9, 8, 7,
            # Side faces (rectangles)
            0, 6, 7, 1,
            1, 7, 8, 2,
            2, 8, 9, 3,
            3, 9, 10, 4,
            4, 10, 11, 5,
            5, 11, 6, 0
        ]
        point3f[] points = [
            # Top hexagon vertices (z = 0.05)
            (0.03, 0, 0.05),            # 0
            (0.015, 0.026, 0.05),       # 1
            (-0.015, 0.026, 0.05),      # 2
            (-0.03, 0, 0.05),           # 3
            (-0.015, -0.026, 0.05),     # 4
            (0.015, -0.026, 0.05),      # 5
            # Bottom hexagon vertices (z = -0.05)
            (0.03, 0, -0.05),           # 6
            (0.015, 0.026, -0.05),      # 7
            (-0.015, 0.026, -0.05),     # 8
            (-0.03, 0, -0.05),          # 9
            (-0.015, -0.026, -0.05),    # 10
            (0.015, -0.026, -0.05)      # 11
        ]
        uniform token subdivisionScheme = "none"
        
        # Material
        def Material "DefaultMaterial" (
        prepend apiSchemas = ["PhysicsMaterialAPI"]
    )
    {
        # 物理材料属性
        float physics:staticFriction = 1.2
        float physics:dynamicFriction = 0.8
        float physics:restitution = 0.1
        
        token outputs:surface.connect = </HexagonalPrism/geometry/DefaultMaterial/DefaultSurfaceShader.outputs:surface>
            
            def Shader "DefaultSurfaceShader"
            {
                uniform token info:id = "UsdPreviewSurface"
                color3f inputs:diffuseColor = (0.8, 0.8, 0.8)
                float inputs:metallic = 0.0
                float inputs:roughness = 0.5
                token outputs:surface
            }
        }
        
        rel material:binding = </HexagonalPrism/geometry/DefaultMaterial>
    }
}