[flake8]
show-source=True
statistics=True
per-file-ignores=*/__init__.py:F401
# E402: Module level import not at top of file
# E501: Line too long
# W503: Line break before binary operator
# E203: Whitespace before ':' -> conflicts with black
# D401: First line should be in imperative mood
# R504: Unnecessary variable assignment before return statement.
# R505: Unnecessary elif after return statement
# SIM102: Use a single if-statement instead of nested if-statements
# SIM117: Merge with statements for context managers that have same scope.
# SIM118: Checks for key-existence checks against dict.keys() calls.
ignore=E402,E501,W503,E203,D401,R504,R505,SIM102,SIM117,SIM118
max-line-length = 120
max-complexity = 30
exclude=_*,.vscode,.git,docs/**
# docstrings
docstring-convention=google
# annotations
suppress-none-returning=True
allow-star-arg-any=True
